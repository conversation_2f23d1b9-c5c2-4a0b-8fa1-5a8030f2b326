﻿@using TeyaUIModels.Model
@using TeyaUIViewModels.ViewModel
@using Microsoft.Extensions.Localization
@using Microsoft.Extensions.Logging
@inject ICosigningService CosigningService
@inject IStringLocalizer<TeyaAIScribeResource> Localizer
@inject ILogger<CosigningComponent> Logger
@inject ISnackbar Snackbar

<MudContainer MaxWidth="MaxWidth.False" Class="cosigning-container">
    @if (ShowCosigningSection)
    {
        <MudPaper Class="cosigning-paper" Elevation="2">
            <!-- Header Section -->
            <MudGrid AlignItems="Center" Justify="Justify.SpaceBetween" Class="cosigning-header">
                <MudItem>
                    <MudStack Row AlignItems="AlignItems.Center" Spacing="2">
                        <MudIcon Icon="@Icons.Material.Filled.VerifiedUser"
                                 Color="Color.Primary"
                                 Size="Size.Medium" />
                        <MudText Typo="Typo.h6" Class="header-title">
                            @Localizer["DocumentSignature"]
                        </MudText>
                    </MudStack>
                </MudItem>
                <MudItem>
                    <MudChip T="string"
                             Size="Size.Small"
                             Color="@GetStatusColor()"
                             Variant="Variant.Filled"
                             Class="status-chip">
                        @GetStatusText()
                    </MudChip>
                </MudItem>
            </MudGrid>

            <!-- Patient Information Section -->
            <MudGrid Class="patient-info-section">
                <MudItem xs="12" md="6">
                    <MudStack Row AlignItems="AlignItems.Center" Spacing="1" Class="patient-info-row">
                        <MudIcon Icon="@Icons.Material.Filled.Person"
                                 Color="Color.Secondary"
                                 Size="Size.Small" />
                        <MudText Typo="Typo.body2" Class="info-label">
                            @Localizer["Patient"]:
                        </MudText>
                        <MudText Typo="Typo.body2" Class="info-value">
                            @PatientName
                        </MudText>
                    </MudStack>
                </MudItem>
                <MudItem xs="12" md="6">
                    <MudStack Row AlignItems="AlignItems.Center" Spacing="1" Class="patient-info-row">
                        <MudIcon Icon="@Icons.Material.Filled.Assignment"
                                 Color="Color.Secondary"
                                 Size="Size.Small" />
                        <MudText Typo="Typo.body2" Class="info-label">
                            @Localizer["RecordID"]:
                        </MudText>
                        <MudText Typo="Typo.body2" Class="info-value">
                            @RecordId.ToString("N")[..8]...
                        </MudText>
                    </MudStack>
                </MudItem>
            </MudGrid>

            <!-- Signature Section -->
            <MudStack Class="signature-section" Spacing="3">
                @if (!CurrentCosigning.IsSigned)
                {
                    <!-- Primary Signature -->
                    <MudPaper Class="signature-block" Elevation="1">
                        <MudStack Spacing="2">
                            <MudStack Row AlignItems="AlignItems.Center" Spacing="1">
                                <MudIcon Icon="@Icons.Material.Filled.Draw" Color="Color.Primary" Size="Size.Small" />
                                <MudText Typo="Typo.subtitle2" Class="signature-title">
                                    @Localizer["PrimarySignature"]
                                </MudText>
                            </MudStack>
                            <MudStack Row Justify="Justify.FlexEnd">
                                <MudButton Variant="Variant.Filled"
                                           Color="Color.Primary"
                                           StartIcon="@Icons.Material.Filled.Draw"
                                           OnClick="SignDocument"
                                           Disabled="@IsProcessing"
                                           Class="signature-button">
                                    @if (IsProcessing)
                                    {
                                        <MudProgressCircular Size="Size.Small" Indeterminate="true" />
                                        <MudText Class="ml-2">@Localizer["Signing"]...</MudText>
                                    }
                                    else
                                    {
                                        @Localizer["SignDocument"]
                                    }
                                </MudButton>
                            </MudStack>
                        </MudStack>
                    </MudPaper>
                }
                else
                {
                    <!-- Signed Status -->
                    <MudPaper Class="signature-block signed" Elevation="1">
                        <MudStack Spacing="1">
                            <MudStack Row AlignItems="AlignItems.Center" Spacing="1">
                                <MudIcon Icon="@Icons.Material.Filled.CheckCircle"
                                         Color="Color.Success"
                                         Size="Size.Small" />
                                <MudText Typo="Typo.subtitle2" Class="signature-title signed">
                                    @Localizer["SignedBy"]: @CurrentCosigning.SignerName
                                </MudText>
                            </MudStack>
                        </MudStack>
                    </MudPaper>

                    @if (RequiresCosignature && !CurrentCosigning.IsCosigned)
                    {
                        <!-- Cosignature Section -->
                        <MudDivider Class="signature-divider" />
                        <MudPaper Class="signature-block" Elevation="1">
                            <MudStack Spacing="2">
                                <MudStack Row AlignItems="AlignItems.Center" Spacing="1">
                                    <MudIcon Icon="@Icons.Material.Filled.SupervisorAccount" Color="Color.Secondary" Size="Size.Small" />
                                    <MudText Typo="Typo.subtitle2" Class="signature-title">
                                        @Localizer["RequiredCosignature"]
                                    </MudText>
                                </MudStack>
                                <MudStack Row Justify="Justify.FlexEnd">
                                    <MudButton Variant="Variant.Outlined"
                                               Color="Color.Secondary"
                                               StartIcon="@Icons.Material.Filled.SupervisorAccount"
                                               OnClick="CosignDocument"
                                               Disabled="@IsProcessing"
                                               Class="signature-button">
                                        @if (IsProcessing)
                                        {
                                            <MudProgressCircular Size="Size.Small" Indeterminate="true" />
                                            <MudText Class="ml-2">@Localizer["Cosigning"]...</MudText>
                                        }
                                        else
                                        {
                                            @Localizer["Cosign"]
                                        }
                                    </MudButton>
                                </MudStack>
                            </MudStack>
                        </MudPaper>
                    }
                    else if (CurrentCosigning.IsCosigned)
                    {
                        <!-- Cosigned Status -->
                        <MudDivider Class="signature-divider" />
                        <MudPaper Class="signature-block signed" Elevation="1">
                            <MudStack Spacing="1">
                                <MudStack Row AlignItems="AlignItems.Center" Spacing="1">
                                    <MudIcon Icon="@Icons.Material.Filled.CheckCircle"
                                             Color="Color.Success"
                                             Size="Size.Small" />
                                    <MudText Typo="Typo.subtitle2" Class="signature-title signed">
                                        @Localizer["CosignedBy"]: @CurrentCosigning.CosignerName
                                    </MudText>
                                </MudStack>
                            </MudStack>
                        </MudPaper>
                    }
                }
            </MudStack>

            <!-- Lock Status -->
            @if (CurrentCosigning.IsLocked)
            {
                <MudAlert Severity="Severity.Info"
                          Variant="Variant.Filled"
                          Dense="true"
                          Class="lock-alert">
                    <MudStack Row AlignItems="AlignItems.Center" Spacing="1">
                        <MudIcon Icon="@Icons.Material.Filled.Lock" Size="Size.Small" />
                        <MudText>@Localizer["DocumentLocked"]</MudText>
                    </MudStack>
                </MudAlert>
            }
        </MudPaper>
    }
</MudContainer>

<style>
    .cosigning-container {
        padding: 0;
        margin: 0;
    }

    .cosigning-paper {
        padding: 24px;
        border-radius: 12px;
        border: 1px solid #e0e0e0;
        background: #ffffff;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    }

    .cosigning-header {
        margin-bottom: 20px;
        padding-bottom: 16px;
        border-bottom: 1px solid #f0f0f0;
    }

    .header-title {
        font-weight: 600;
        color: #1976d2;
        margin: 0;
    }

    .status-chip {
        font-weight: 500;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        font-size: 0.75rem;
    }

    .patient-info-section {
        margin-bottom: 24px;
        padding: 16px;
        background: #f8f9fa;
        border-radius: 8px;
    }

    .patient-info-row {
        margin-bottom: 8px;
    }

    .info-label {
        font-weight: 500;
        color: #666;
        margin-right: 8px;
    }

    .info-value {
        font-weight: 600;
        color: #333;
    }

    .signature-section {
        margin-top: 24px;
    }

    .signature-block {
        padding: 20px;
        border-radius: 8px;
        border: 1px solid #e0e0e0;
        background: #ffffff;
        transition: all 0.2s ease;
    }

    .signature-block:hover {
        border-color: #1976d2;
        box-shadow: 0 2px 8px rgba(25, 118, 210, 0.1);
    }

    .signature-block.signed {
        background: #f8fff8;
        border-color: #4caf50;
    }

    .signature-title {
        font-weight: 600;
        color: #333;
        margin: 0;
    }

    .signature-title.signed {
        color: #2e7d32;
    }

    .signature-button {
        min-width: 140px;
        font-weight: 500;
        text-transform: none;
        border-radius: 6px;
    }

    .signature-divider {
        margin: 16px 0;
        opacity: 0.6;
    }

    .lock-alert {
        margin-top: 16px;
        border-radius: 8px;
    }
</style>
﻿@using TeyaUIModels.Model
@using TeyaUIViewModels.ViewModel
@using Microsoft.Extensions.Localization
@using Microsoft.Extensions.Logging
@inject ICosigningService CosigningService
@inject IStringLocalizer<TeyaAIScribeResource> Localizer
@inject ILogger<CosigningComponent> Logger
@inject ISnackbar Snackbar

<MudContainer MaxWidth="MaxWidth.False" Class="cosigning-container">
    @if (ShowCosigningSection)
    {
        <MudPaper Class="cosigning-paper" Elevation="2">
            <!-- Header Section -->
            <MudGrid AlignItems="Center" Justify="Justify.SpaceBetween" Class="cosigning-header">
                <MudItem>
                    <MudStack Row AlignItems="AlignItems.Center" Spacing="2">
                        <MudIcon Icon="@Icons.Material.Filled.VerifiedUser"
                                 Color="Color.Primary"
                                 Size="Size.Medium" />
                        <MudText Typo="Typo.h6" Class="header-title">
                            @Localizer["DocumentSignature"]
                        </MudText>
                    </MudStack>
                </MudItem>
                <MudItem>
                    <MudChip Size="Size.Small"
                             Color="@GetStatusColor()"
                             Variant="Variant.Filled"
                             Class="status-chip">
                        @GetStatusText()
                    </MudChip>
                </MudItem>
            </MudGrid>

            <!-- Patient Information Section -->
            <MudGrid Class="patient-info-section">
                <MudItem xs="12" md="6">
                    <MudStack Row AlignItems="AlignItems.Center" Spacing="1" Class="patient-info-row">
                        <MudIcon Icon="@Icons.Material.Filled.Person"
                                 Color="Color.Secondary"
                                 Size="Size.Small" />
                        <MudText Typo="Typo.body2" Class="info-label">
                            @Localizer["Patient"]:
                        </MudText>
                        <MudText Typo="Typo.body2" Class="info-value">
                            @PatientName
                        </MudText>
                    </MudStack>
                </MudItem>
                <MudItem xs="12" md="6">
                    <MudStack Row AlignItems="AlignItems.Center" Spacing="1" Class="patient-info-row">
                        <MudIcon Icon="@Icons.Material.Filled.Assignment"
                                 Color="Color.Secondary"
                                 Size="Size.Small" />
                        <MudText Typo="Typo.body2" Class="info-label">
                            @Localizer["RecordID"]:
                        </MudText>
                        <MudText Typo="Typo.body2" Class="info-value">
                            @RecordId.ToString("N")[..8]...
                        </MudText>
                    </MudStack>
                </MudItem>
            </MudGrid>

            <!-- Signature Section -->
            <MudStack Class="signature-section" Spacing="3">
                @if (!CurrentCosigning.IsSigned)
                {
                    <!-- Primary Signature -->
                    <MudPaper Class="signature-block" Elevation="1">
                        <MudStack Spacing="2">
                            <MudStack Row AlignItems="AlignItems.Center" Spacing="1">
                                <MudIcon Icon="@Icons.Material.Filled.Draw" Color="Color.Primary" Size="Size.Small" />
                                <MudText Typo="Typo.subtitle2" Class="signature-title">
                                    @Localizer["PrimarySignature"]
                                </MudText>
                            </MudStack>
                            <MudStack Row Justify="Justify.FlexEnd">
                                <MudButton Variant="Variant.Filled"
                                           Color="Color.Primary"
                                           StartIcon="@Icons.Material.Filled.Draw"
                                           OnClick="SignDocument"
                                           Disabled="@IsProcessing"
                                           Class="signature-button">
                                    @if (IsProcessing)
                                    {
                                        <MudProgressCircular Size="Size.Small" Indeterminate="true" />
                                        <MudText Class="ml-2">@Localizer["Signing"]...</MudText>
                                    }
                                    else
                                    {
                                        @Localizer["SignDocument"]
                                    }
                                </MudButton>
                            </MudStack>
                        </MudStack>
                    </MudPaper>
                }
                else
                {
                    <!-- Signed Status -->
                    <MudPaper Class="signature-block signed" Elevation="1">
                        <MudStack Spacing="1">
                            <MudStack Row AlignItems="AlignItems.Center" Spacing="1">
                                <MudIcon Icon="@Icons.Material.Filled.CheckCircle"
                                         Color="Color.Success"
                                         Size="Size.Small" />
                                <MudText Typo="Typo.subtitle2" Class="signature-title signed">
                                    @Localizer["SignedBy"]: @CurrentCosigning.SignerName
                                </MudText>
                            </MudStack>
                        </MudStack>
                    </MudPaper>

                    @if (RequiresCosignature && !CurrentCosigning.IsCosigned)
                    {
                        <!-- Cosignature Section -->
                        <MudDivider Class="signature-divider" />
                        <MudPaper Class="signature-block" Elevation="1">
                            <MudStack Spacing="2">
                                <MudStack Row AlignItems="AlignItems.Center" Spacing="1">
                                    <MudIcon Icon="@Icons.Material.Filled.SupervisorAccount" Color="Color.Secondary" Size="Size.Small" />
                                    <MudText Typo="Typo.subtitle2" Class="signature-title">
                                        @Localizer["RequiredCosignature"]
                                    </MudText>
                                </MudStack>
                                <MudStack Row Justify="Justify.FlexEnd">
                                    <MudButton Variant="Variant.Outlined"
                                               Color="Color.Secondary"
                                               StartIcon="@Icons.Material.Filled.SupervisorAccount"
                                               OnClick="CosignDocument"
                                               Disabled="@IsProcessing"
                                               Class="signature-button">
                                        @if (IsProcessing)
                                        {
                                            <MudProgressCircular Size="Size.Small" Indeterminate="true" />
                                            <MudText Class="ml-2">@Localizer["Cosigning"]...</MudText>
                                        }
                                        else
                                        {
                                            @Localizer["Cosign"]
                                        }
                                    </MudButton>
                                </MudStack>
                            </MudStack>
                        </MudPaper>
                    }
                    else if (CurrentCosigning.IsCosigned)
                    {
                        <!-- Cosigned Status -->
                        <MudDivider Class="signature-divider" />
                        <MudPaper Class="signature-block signed" Elevation="1">
                            <MudStack Spacing="1">
                                <MudStack Row AlignItems="AlignItems.Center" Spacing="1">
                                    <MudIcon Icon="@Icons.Material.Filled.CheckCircle"
                                             Color="Color.Success"
                                             Size="Size.Small" />
                                    <MudText Typo="Typo.subtitle2" Class="signature-title signed">
                                        @Localizer["CosignedBy"]: @CurrentCosigning.CosignerName
                                    </MudText>
                                </MudStack>
                            </MudStack>
                        </MudPaper>
                    }
                }
            </MudStack>

            <!-- Lock Status -->
            @if (CurrentCosigning.IsLocked)
            {
                <MudAlert Severity="Severity.Info"
                          Variant="Variant.Filled"
                          Dense="true"
                          Class="lock-alert">
                    <MudStack Row AlignItems="AlignItems.Center" Spacing="1">
                        <MudIcon Icon="@Icons.Material.Filled.Lock" Size="Size.Small" />
                        <MudText>@Localizer["DocumentLocked"]</MudText>
                    </MudStack>
                </MudAlert>
            }
        </MudPaper>
    }
</MudContainer>
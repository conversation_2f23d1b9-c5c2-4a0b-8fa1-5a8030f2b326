﻿@using TeyaUIModels.Model
@using TeyaUIViewModels.ViewModel
@using Microsoft.Extensions.Localization
@using Microsoft.Extensions.Logging
@inject ICosigningService CosigningService
@inject IStringLocalizer<TeyaAIScribeResource> Localizer
@inject ILogger<CosigningComponent> Logger
@inject ISnackbar Snackbar

<MudContainer MaxWidth="MaxWidth.False" Class="cosigning-container">
    @if (ShowCosigningSection)
    {
        <MudPaper Class="cosigning-paper" Elevation="2">
            <!-- Header Section -->
            <MudGrid AlignItems="Center" Justify="Justify.SpaceBetween" Class="cosigning-header">
                <MudItem>
                    <MudStack Row AlignItems="AlignItems.Center" Spacing="2">
                        <MudIcon Icon="@Icons.Material.Filled.VerifiedUser"
                                 Color="Color.Primary"
                                 Size="Size.Medium" />
                        <MudText Typo="Typo.h6" Class="header-title">
                            @Localizer["DocumentSignature"]
                        </MudText>
                    </MudStack>
                </MudItem>
                <MudItem>
                    <MudChip T="string"
                             Size="Size.Small"
                             Color="@GetStatusColor()"
                             Variant="Variant.Filled"
                             Class="status-chip">
                        @GetStatusText()
                    </MudChip>
                </MudItem>
            </MudGrid>

            <!-- Signature Section -->
            <MudStack Class="signature-section" Spacing="3">
                @if (!CurrentCosigning.IsSigned)
                {
                    <!-- Primary Signature -->
                    <MudPaper Class="signature-block" Elevation="1">
                        <MudStack Spacing="2">
                            <MudStack Row AlignItems="AlignItems.Center" Spacing="1">
                                <MudIcon Icon="@Icons.Material.Filled.Draw" Color="Color.Primary" Size="Size.Small" />
                                <MudText Typo="Typo.subtitle2" Class="signature-title">
                                    @Localizer["PrimarySignature"]
                                </MudText>
                            </MudStack>
                            <MudStack Row Justify="Justify.FlexEnd">
                                <MudButton Variant="Variant.Filled"
                                           Color="Color.Primary"
                                           StartIcon="@Icons.Material.Filled.Draw"
                                           OnClick="NavigateToSigningPage"
                                           Disabled="@IsProcessing"
                                           Class="signature-button">
                                    @Localizer["SignDocument"]
                                </MudButton>
                            </MudStack>
                        </MudStack>
                    </MudPaper>
                }
                else
                {
                    <!-- Signed Status -->
                    <MudPaper Class="signature-block signed" Elevation="1">
                        <MudStack Spacing="1">
                            <MudStack Row AlignItems="AlignItems.Center" Spacing="1">
                                <MudIcon Icon="@Icons.Material.Filled.CheckCircle"
                                         Color="Color.Success"
                                         Size="Size.Small" />
                                <MudText Typo="Typo.subtitle2" Class="signature-title signed">
                                    @Localizer["SignedBy"]: @CurrentCosigning.SignerName
                                </MudText>
                            </MudStack>
                        </MudStack>
                    </MudPaper>

                    @if (RequiresCosignature && !CurrentCosigning.IsCosigned)
                    {
                        <!-- Cosignature Section -->
                        <MudDivider Class="signature-divider" />
                        <MudPaper Class="signature-block" Elevation="1">
                            <MudStack Spacing="2">
                                <MudStack Row AlignItems="AlignItems.Center" Spacing="1">
                                    <MudIcon Icon="@Icons.Material.Filled.SupervisorAccount" Color="Color.Secondary" Size="Size.Small" />
                                    <MudText Typo="Typo.subtitle2" Class="signature-title">
                                        @Localizer["RequiredCosignature"]
                                    </MudText>
                                </MudStack>
                                <MudStack Row Justify="Justify.FlexEnd">
                                    <MudButton Variant="Variant.Outlined"
                                               Color="Color.Secondary"
                                               StartIcon="@Icons.Material.Filled.SupervisorAccount"
                                               OnClick="NavigateToSigningPage"
                                               Disabled="@IsProcessing"
                                               Class="signature-button">
                                        @Localizer["Cosign"]
                                    </MudButton>
                                </MudStack>
                            </MudStack>
                        </MudPaper>
                    }
                    else if (CurrentCosigning.IsCosigned)
                    {
                        <!-- Cosigned Status -->
                        <MudDivider Class="signature-divider" />
                        <MudPaper Class="signature-block signed" Elevation="1">
                            <MudStack Spacing="1">
                                <MudStack Row AlignItems="AlignItems.Center" Spacing="1">
                                    <MudIcon Icon="@Icons.Material.Filled.CheckCircle"
                                             Color="Color.Success"
                                             Size="Size.Small" />
                                    <MudText Typo="Typo.subtitle2" Class="signature-title signed">
                                        @Localizer["CosignedBy"]: @CurrentCosigning.CosignerName
                                    </MudText>
                                </MudStack>
                            </MudStack>
                        </MudPaper>
                    }
                }
            </MudStack>

            <!-- Lock Status -->
            @if (CurrentCosigning.IsLocked)
            {
                <MudAlert Severity="Severity.Info"
                          Variant="Variant.Filled"
                          Dense="true"
                          Class="lock-alert">
                    <MudStack Row AlignItems="AlignItems.Center" Spacing="1">
                        <MudIcon Icon="@Icons.Material.Filled.Lock" Size="Size.Small" />
                        <MudText>@Localizer["DocumentLocked"]</MudText>
                    </MudStack>
                </MudAlert>
            }
        </MudPaper>
    }
</MudContainer>

<style>
    .cosigning-container {
        padding: 0;
        margin: 0;
        width: 100%;
    }

    .cosigning-paper {
        padding: 16px;
        border-radius: 4px;
        border: 1px solid #e0e0e0;
        background: #ffffff;
        margin-bottom: 16px;
        width: 100%;
    }

    .cosigning-header {
        margin-bottom: 16px;
        padding-bottom: 12px;
        border-bottom: 1px solid #f0f0f0;
    }

    .header-title {
        font-weight: 600;
        color: #1976d2;
        margin: 0;
        font-size: 1rem;
    }

    .status-chip {
        font-weight: 500;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        font-size: 0.75rem;
    }

    .signature-section {
        margin-top: 0;
    }

    .signature-block {
        padding: 16px;
        border-radius: 4px;
        border: 1px solid #e0e0e0;
        background: #ffffff;
        transition: all 0.2s ease;
    }

    .signature-block:hover {
        border-color: #1976d2;
        box-shadow: 0 2px 8px rgba(25, 118, 210, 0.1);
    }

    .signature-block.signed {
        background: #f8fff8;
        border-color: #4caf50;
    }

    .signature-title {
        font-weight: 600;
        color: #333;
        margin: 0;
        font-size: 0.875rem;
    }

    .signature-title.signed {
        color: #2e7d32;
    }

    .signature-button {
        min-width: 120px;
        font-weight: 500;
        text-transform: none;
        border-radius: 4px;
        font-size: 0.875rem;
    }

    .signature-divider {
        margin: 12px 0;
        opacity: 0.6;
    }

    .lock-alert {
        margin-top: 12px;
        border-radius: 4px;
    }
</style>
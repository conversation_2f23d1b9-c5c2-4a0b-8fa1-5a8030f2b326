﻿@page "/CosigningPage"
@using Microsoft.AspNetCore.Authorization
@using TeyaWebApp.Components.Layout
@using TeyaWebApp.TeyaAIScribeResources
@using MudBlazor
@using Syncfusion.Blazor.RichTextEditor
@using TeyaUIViewModels.ViewModel
@using System
@attribute [Authorize]
@inject IStringLocalizer<TeyaAIScribeResource> Localizer
@inject ILogger<CosigningPage> Logger
@inject IDialogService DialogService

<div style="position: relative;">
    <MudTextField Variant="Variant.Outlined"
                  Label="Signed/Cosigned Progress Notes"
                  Lines="5"
                  @bind-Value="@richTextContent"
                  ReadOnly="true"
                  FullWidth="true"
                  InputType="InputType.Text"
                  Multiline="true"
                  Style="max-height: 400px; height: 100%; overflow-y: auto; white-space: pre-wrap;
                         font-family: monospace; margin-bottom: 10px;" />
    <div style="position: absolute; top: 8px; right: 8px;">
        <MudButton Size="Size.Small"
                   OnClick="OpenNewDialogBox"
                   Disabled="_isLocked"
                   Style="background-color: #f5f5f5; border: 1px solid #e0e0e0;">
            Sign
        </MudButton>
    </div>
</div>

<MudDialog @bind-IsVisible="_isDialogVisible"
           Options="_dialogOptions"
           OnBackdropClick="HandleBackdropClick">
    <TitleContent>
        <div style="display: flex; justify-content: space-between; align-items: center; width: 100%; padding: 16px;">
            <MudText Typo="Typo.h6" Style="font-size: 1rem; font-weight: 600;">
                @Localizer["Signed/Cosigned Progress Notes"]
            </MudText>
            <MudIconButton Icon="@Icons.Material.Filled.Close"
                           Size="Size.Small"
                           OnClick="CancelData"
                           Style="margin: -4px;" />
        </div>
    </TitleContent>

    <DialogContent>
        <div style="height: calc(90vh - 150px); display: flex; padding: 0; overflow: hidden;">
            <!-- Left Section -->
            <div style="flex: 0 0 60%; padding: 16px; border-right: 1px solid #e0e0e0; overflow-y: auto;">
                <div style="margin-bottom: 24px;">
                    <div style="background: #f5f5f5; padding: 16px; border-radius: 4px;">
                        <MudText Typo="Typo.body1" Class="mb-2">@Localizer["Patient Name"]: @patient</MudText>
                    </div>
                </div>

                <div style="margin-bottom: 24px;">
                    <MudText Typo="Typo.subtitle1" Class="mb-2">@Localizer["Notes"]</MudText>
                    <MudTextField Variant="Variant.Outlined"
                                  Lines="20"
                                  ReadOnly="true"
                                  @bind-Value="@patientNotes"
                                  FullWidth="true"
                                  Class="mb-4"
                                  InputType="InputType.Text"
                                  Multiline="true"
                                  Style="max-height: 400px; height: 100%; overflow-y: auto; white-space: pre-wrap; font-family: monospace;" />
                </div>
            </div>

            <!-- Right Section -->
            <div style="flex: 1; padding: 16px; overflow-y: auto;">
                <MudText Typo="Typo.h6" Class="mb-4">@Localizer["Assign To Progress Notes"]</MudText>

                <div style="display: flex; gap: 16px; margin-bottom: 24px;">
                    <!-- From dropdown (Patient's PCP) -->
                    <MudSelect @bind-Value="pcpName"
                               Label="From"
                               Variant="Variant.Outlined"
                               Size="Size.Small"
                               Style="flex: 1;"
                               Disabled="true">
                        <MudSelectItem Value="@pcpName">@pcpName</MudSelectItem>
                    </MudSelect>

                    <!-- To dropdown (Only visible for Co-Sign) -->
                    @if (isCoSign)
                    {
                        <MudSelect @bind-Value="_selectedToProvider"
                                   Label="To"
                                   Variant="Variant.Outlined"
                                   Size="Size.Small"
                                   Style="flex: 1;">
                            @foreach (var provider in ProviderList)
                            {
                                <MudSelectItem Value="@provider">@provider</MudSelectItem>
                            }
                        </MudSelect>
                    }
                </div>

                <div class="mb-4 d-flex align-items-center">
                    <MudText Typo="Typo.body1" Class="me-2">@Localizer["Status:"]</MudText>
                    <MudRadioGroup @bind-Value="isCoSign" Class="d-flex gap-3">
                        <MudRadio Value="true" Color="Color.Primary" Dense="true">@Localizer["Co-Sign"]</MudRadio>
                        <MudRadio Value="false" Color="Color.Primary" Dense="true">@Localizer["Sign"]</MudRadio>
                    </MudRadioGroup>
                </div>

                <!-- Additional Notes Section -->
                <div class="mb-4">
                    <MudText Typo="Typo.subtitle2" Class="mb-2">@Localizer["Additional Notes"]</MudText>
                    <MudTextField Variant="Variant.Outlined"
                                  Lines="3"
                                  @bind-Value="@additionalNotes"
                                  FullWidth="true"
                                  Placeholder="@Localizer["Enter any additional notes..."]"
                                  InputType="InputType.Text"
                                  Multiline="true"
                                  Style="font-family: inherit;" />
                </div>
            </div>
        </div>
    </DialogContent>

    <DialogActions>
        <div class="d-flex justify-end gap-2 pa-4" style="border-top: 1px solid #e0e0e0; width: 100%;">
            <MudButton Variant="Variant.Outlined"
                       Color="Color.Secondary"
                       OnClick="CancelData"
                       Dense="true"
                       Style="min-width: 120px;">
                @Localizer["Cancel"]
            </MudButton>
            <MudButton Variant="Variant.Filled"
                       Color="Color.Primary"
                       OnClick="HandleOk"
                       Disabled="@_isLocked"
                       Dense="true"
                       Style="min-width: 120px;">
                @Localizer["Sign"]
            </MudButton>
        </div>
    </DialogActions>
</MudDialog>

<style>
    .mud-dialog .mud-dialog-content {
        padding: 0 !important;
    }

    .mud-dialog .mud-dialog-title {
        padding: 0 !important;
        background: #f8f9fa;
        border-bottom: 1px solid #e0e0e0;
    }

    .mud-dialog .mud-dialog-actions {
        padding: 0 !important;
    }

    .mud-textfield .mud-input-outlined {
        border-radius: 8px;
    }

    .mud-select .mud-input-outlined {
        border-radius: 8px;
    }

    .mud-radio-group .mud-radio {
        margin-right: 16px;
    }

    .mud-button {
        border-radius: 6px;
        font-weight: 500;
        text-transform: none;
    }

    .mud-button-filled {
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    .mud-button-outlined {
        border-width: 1.5px;
    }

    .mud-icon-button {
        border-radius: 50%;
        transition: all 0.2s ease;
    }

    .mud-icon-button:hover {
        background-color: #f5f5f5;
        transform: scale(1.05);
    }
</style>




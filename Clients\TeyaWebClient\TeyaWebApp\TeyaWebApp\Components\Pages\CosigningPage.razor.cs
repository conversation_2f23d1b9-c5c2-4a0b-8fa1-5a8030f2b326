﻿using Syncfusion.Blazor.Popups;
using System.Text.Json;
using System.Collections.Generic;
using System.Text.Json.Serialization;
using Syncfusion.Blazor.RichTextEditor;
using TeyaUIModels.Model;
using MudBlazor;
using TeyaUIViewModels.ViewModel;
using Microsoft.AspNetCore.Components;
using Syncfusion.Blazor.Grids;
using System.Threading;
using System.Linq;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Localization;
using Syncfusion.Blazor;
using System.Text;
using System.Security.Cryptography.Xml;
using Syncfusion.Blazor.Inputs;
using Microsoft.Graph.Models;
using Microsoft.CognitiveServices.Speech.Transcription;
using static Azure.Core.HttpHeader;
using System;
using static TeyaWebApp.Components.Pages.Appointments;
using TeyaUIModels.ViewModel;


namespace TeyaWebApp.Components.Pages
{
    public partial class CosigningPage
    {
        private MudDialog _dialog;
        [Inject] private ISnackbar Snackbar { get; set; }
        [Inject] private ILogger<CosigningPage> _logger { get; set; }
        [Inject] private IStringLocalizer<CosigningPage> _localizer { get; set; }
        [Inject] private PatientService _PatientService { get; set; }
        [Inject] public IProgressNotesService ProgressNotesService { get; set; }
        [Inject] private ICosigningService CosigningService { get; set; }
        [Inject] private IOrganizationService OrganizationService { get; set; }
        [Inject] private ActiveUser _ActiveUser { get; set; }
        [Inject] private IMemberService MemberService { get; set; }
        [Parameter] public Guid RecordId { get; set; }
        public bool Dense_Radio { get; set; } = true;
        public bool IsSigned { get; set; } = false;
        private Record records;
        private string patientNotes;
        public string patient { get; set; }
        public string pcpName { get; set; }
        public string pcpId { get; set; }
        private Guid patientId { get; set; }
        private Guid OrgID { get; set; }
        private bool flag { get; set; } = false;
        private SfGrid<Patient> PatientGrid;
        private List<Member> users = new List<Member>();
        private List<string> ProviderList = new List<string>();

        private Member _selectedToProvider;
        private string richTextContent = string.Empty;
        private bool Subscription = false;

        private TeyaUIModels.Model.Cosigning _existingCosigning;
        private bool _isLocked;
        private string notesBody = string.Empty;
        private string signedText = string.Empty;
        private string cosignedText = string.Empty;

        // Dialog properties
        private bool _isDialogVisible = false;
        private MudBlazor.DialogOptions _dialogOptions = new() { MaxWidth = MaxWidth.Large, FullWidth = true };

        // Additional properties
        private bool isCoSign = false;
        private string additionalNotes = string.Empty;



        private async Task LoadAICardNotes()
        {
            try
            {
                patientNotes = string.Empty;
                var cosignings = await CosigningService.GetCosigningsByRecordId(RecordId);
                _existingCosigning = cosignings.FirstOrDefault();

                if (_existingCosigning != null)
                {
                    _isLocked = _existingCosigning.IsLocked;

                    var allLines = _existingCosigning.Notes?.Split('\n') ?? Array.Empty<string>();
                    notesBody = string.Join("\n", allLines.Where(line =>
                        !line.StartsWith("Electronically signed by") &&
                        !line.StartsWith("Electronically co-signed") &&
                        !line.StartsWith("Notes Locked!!")));

                    if (_existingCosigning.IsSigned)
                        signedText = $"Electronically signed by {_existingCosigning.SignerName} on {_existingCosigning.LastUpdated:MM/dd/yyyy}\n";

                    if (_existingCosigning.IsCosigned)
                        cosignedText = $"Electronically co-signed {_existingCosigning.CosignerName} on {_existingCosigning.LastUpdated:MM/dd/yyyy}\n";
                }
                else
                {
                    records = await ProgressNotesService.GetRecordByIdAsync(RecordId, OrgID, Subscription);
                    var notesDictionary = JsonSerializer.Deserialize<Dictionary<string, Dictionary<string, string>>>(records.Notes);
                    var formattedNotes = new StringBuilder();

                    foreach (var section in notesDictionary)
                    {
                        formattedNotes.AppendLine($"{section.Key}:");
                        foreach (var field in section.Value)
                        {
                            formattedNotes.AppendLine($"  {field.Key}: {field.Value}");
                        }
                        formattedNotes.AppendLine();
                    }

                    notesBody = formattedNotes.ToString();
                }

                patientNotes = notesBody + signedText + cosignedText;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading notes");
                Snackbar.Add(_localizer["AICardLoadError"], Severity.Error);
                patientNotes = "Error loading notes";
            }
        }

        protected override async Task OnInitializedAsync()
        {
            try
            {
                OrgID = await OrganizationService.GetOrganizationIdByNameAsync(_ActiveUser.OrganizationName);
                ProviderList = await MemberService.GetProviderlistAsync(OrgID, Subscription);
                if (_PatientService.PatientData != null)
                {
                    patient = _PatientService.PatientData.Name;
                    patientId = _PatientService.PatientData.Id;
                }
                pcpName = $"{_ActiveUser.givenName} {_ActiveUser.surname}";
                pcpId = _ActiveUser.id;

                await LoadAICardNotes();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Unexpected error during initialization");
                Snackbar.Add(_localizer["UnexpectedInitializationError"], Severity.Error);
                patientNotes = "Unexpected error loading data";
            }
        }

        private async Task SaveData()
        {
            try
            {
                if (_existingCosigning == null && Dense_Radio)
                {
                    Snackbar.Add(_localizer["Sign before co-signing"], Severity.Error);
                    return;
                }

                var now = DateTime.Now;
                var isCosign = Dense_Radio;

                if (_existingCosigning == null)
                {
                    _existingCosigning = new TeyaUIModels.Model.Cosigning
                    {
                        Id = Guid.NewGuid(),
                        RecordId = RecordId,
                        Notes = patientNotes,
                        IsSigned = true,
                        IsCosigned = false,
                        IsLocked = false,
                        SignerId = Guid.Parse(_ActiveUser.id),
                        CosignerName = string.Empty,
                        CosignerId = Guid.Parse(_ActiveUser.id),
                        OrganizationId = OrgID,
                        LastUpdated = now,
                        SignerName = pcpName,
                        Date = now,
                    };

                    await CosigningService.AddCosigning(new List<TeyaUIModels.Model.Cosigning> { _existingCosigning });
                }
                else
                {
                    _existingCosigning.Notes = patientNotes;

                    if (isCosign)
                    {
                        if (!_existingCosigning.IsSigned)
                        {
                            Snackbar.Add(_localizer["Note must be signed first"], Severity.Error);
                            return;
                        }

                        _existingCosigning.IsCosigned = true;
                        _existingCosigning.CosignerName = _selectedToProvider.UserName;
                        _existingCosigning.CosignerId = _selectedToProvider.Id;
                        _existingCosigning.Date = now;
                    }
                    else
                    {
                        _existingCosigning.IsSigned = true;
                        _existingCosigning.LastUpdated = now;
                        _existingCosigning.SignerName = pcpName;
                        _existingCosigning.SignerId = Guid.Parse(_ActiveUser.id);
                    }

                    await CosigningService.UpdateCosigning(_existingCosigning);
                }

                Snackbar.Add(_localizer["Changes saved successfully"], Severity.Success);
                flag = true;
            }
            catch (Exception ex)
            {
                Snackbar.Add(_localizer["Save failed"], Severity.Error);
                _logger.LogError(ex, "Error saving cosigning data");
            }
        }

        // Dialog methods
        private void OpenNewDialogBox()
        {
            _isDialogVisible = true;
            richTextContent = patientNotes;
        }

        private void HandleBackdropClick()
        {
            // Optionally close dialog on backdrop click
            // _isDialogVisible = false;
        }

        private void CancelData()
        {
            _isDialogVisible = false;
            additionalNotes = string.Empty;
        }

        private async Task HandleOk()
        {
            try
            {
                await SaveData();
                _isDialogVisible = false;
                await LoadAICardNotes(); // Refresh the content
                richTextContent = patientNotes;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in HandleOk");
                Snackbar.Add(_localizer["Error processing request"], Severity.Error);
            }
        }
    }
}